import torch
from torch import nn

from ...core.alg_frame.client_trainer import <PERSON>lient<PERSON>rainer
from ...utils.model_utils import check_device
import logging

# It's good practice to have a helper for this
def _get_flat_params_from_model(model):
    """Helper to flatten model parameters into a single vector."""
    return torch.cat([p.view(-1) for p in model.parameters() if p.requires_grad])

class ScaffoldModelTrainer(ClientTrainer):
    def get_model_params(self):
        return self.model.cpu().state_dict()

    def set_model_params(self, model_parameters):
        self.model.load_state_dict(model_parameters)




    def train(self, train_data, device, args, c_model_global_params, c_model_local_params):
        model = self.model
        model.to(device)
        model.train()

        criterion = nn.CrossEntropyLoss().to(device)
        if args.client_optimizer == "sgd":
            optimizer = torch.optim.SGD(
                filter(lambda p: p.requires_grad, self.model.parameters()),
                lr=args.learning_rate,
            )
        else:
            optimizer = torch.optim.Adam(
                filter(lambda p: p.requires_grad, self.model.parameters()),
                lr=args.learning_rate,
                weight_decay=args.weight_decay,
                amsgrad=True,
            )

        ## 1. PRE-COMPUTE THE CONTROL VARIATE CORRECTION (Fast & Efficient)
        # We calculate the (c_local - c_global) term once, outside the loop.
        # This avoids the slow, incorrect loop from the original code.
        control_variate_update_term = []
        for name, param in model.named_parameters():
            if param.requires_grad:
                # Ensure control variates are on the correct device
                c_global = c_model_global_params[name].to(device)
                c_local = c_model_local_params[name].to(device)
                control_variate_update_term.append((c_local - c_global).view(-1))
        
        # Flatten into a single vector for a fast dot product later
        flat_control_variate_update = torch.cat(control_variate_update_term)

        iteration_cnt = 0
        for epoch in range(args.epochs):
            for batch_idx, (x, labels) in enumerate(train_data):
                x, labels = x.to(device), labels.to(device)
                optimizer.zero_grad()
                
                log_probs = model(x)
                loss = criterion(log_probs, labels)

                ## 2. GET CURRENT MODEL PARAMETERS & APPLY SCAFFOLD CORRECTION
                # This is the mathematically correct way to apply the SCAFFOLD update
                flat_model_params = _get_flat_params_from_model(model)
                scaffold_correction_term = torch.dot(flat_model_params, flat_control_variate_update)

                # Add the correction to the main loss
                total_loss = loss + scaffold_correction_term
                
                # The backward pass now computes the TRUE SCAFFOLD gradient
                total_loss.backward()

                ## 3. ENABLE GRADIENT CLIPPING (Crucial for Stability)
                # This was commented out, which was the direct cause of NaN errors.
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

                optimizer.step()
                
                iteration_cnt += 1
        
        # NOTE: The incorrect manual update loop has been completely removed.
        return iteration_cnt

    def train_one_epoch_scaffold(self, train_data, device, args, c_model_global_params, c_model_local_params, epoch_idx):
        """
        Train the model for one epoch with SCAFFOLD. Used by ScaffoldClient to capture intermediate states.
        """
        model = self.model
        model.to(device)
        model.train()

        criterion = nn.CrossEntropyLoss().to(device)

        # Initialize optimizer if not already done
        if not hasattr(self, '_optimizer'):
            if args.client_optimizer == "sgd":
                self._optimizer = torch.optim.SGD(
                    filter(lambda p: p.requires_grad, self.model.parameters()),
                    lr=args.learning_rate,
                )
            else:
                self._optimizer = torch.optim.Adam(
                    filter(lambda p: p.requires_grad, self.model.parameters()),
                    lr=args.learning_rate,
                    weight_decay=args.weight_decay,
                    amsgrad=True,
                )

        ## 1. PRE-COMPUTE THE CONTROL VARIATE CORRECTION (Fast & Efficient)
        # We calculate the (c_local - c_global) term once, outside the loop.
        # This avoids the slow, incorrect loop from the original code.
        control_variate_update_term = []
        for name, param in model.named_parameters():
            if param.requires_grad:
                # Ensure control variates are on the correct device
                c_global = c_model_global_params[name].to(device)
                c_local = c_model_local_params[name].to(device)
                control_variate_update_term.append((c_local - c_global).view(-1))

        # Flatten into a single vector for a fast dot product later
        flat_control_variate_update = torch.cat(control_variate_update_term)

        iteration_cnt = 0
        for batch_idx, (x, labels) in enumerate(train_data):
            x, labels = x.to(device), labels.to(device)
            self._optimizer.zero_grad()

            log_probs = model(x)
            loss = criterion(log_probs, labels)

            ## 2. GET CURRENT MODEL PARAMETERS & APPLY SCAFFOLD CORRECTION
            # This is the mathematically correct way to apply the SCAFFOLD update
            flat_model_params = _get_flat_params_from_model(model)
            scaffold_correction_term = torch.dot(flat_model_params, flat_control_variate_update)

            # Add the correction to the main loss
            total_loss = loss + scaffold_correction_term

            # The backward pass now computes the TRUE SCAFFOLD gradient
            total_loss.backward()

            ## 3. ENABLE GRADIENT CLIPPING (Crucial for Stability)
            # This was commented out, which was the direct cause of NaN errors.
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

            self._optimizer.step()

            iteration_cnt += 1

        logging.info(
            "Client Index = {}\tEpoch: {}\tIterations: {}".format(
                self.id, epoch_idx, iteration_cnt
            )
        )

        return iteration_cnt

    def _check_control_variates(self, c_dict):
        """Validate control variate values"""
        for name, tensor in c_dict.items():
            if not torch.is_tensor(tensor):
                logging.error(f"Control variate {name} is not a tensor!")
                c_dict[name] = torch.zeros_like(getattr(self.model, name).data)
            elif torch.isnan(tensor).any() or torch.isinf(tensor).any():
                logging.warning(f"Control variate {name} contains NaN/Inf! Resetting")
                c_dict[name] = torch.zeros_like(tensor)
        return c_dict
        
    def test(self, test_data, device, args):
        model = self.model

        model.to(device)
        model.eval()

        metrics = {"test_correct": 0, "test_loss": 0, "test_total": 0}

        criterion = nn.CrossEntropyLoss().to(device)

        with torch.no_grad():
            for batch_idx, (x, target) in enumerate(test_data):
                x = x.to(device)
                target = target.to(device)
                pred = model(x)
                loss = criterion(pred, target)  # pylint: disable=E1102

                _, predicted = torch.max(pred, -1)
                correct = predicted.eq(target).sum()

                metrics["test_correct"] += correct.item()
                metrics["test_loss"] += loss.item() * target.size(0)
                metrics["test_total"] += target.size(0)
        return metrics
