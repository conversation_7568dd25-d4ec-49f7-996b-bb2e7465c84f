from copy import deepcopy
import torch
import logging


class Client:
    def __init__(
        self, client_idx, local_training_data, local_test_data, local_sample_number, args, device, model_trainer,
    ):
        self.client_idx = client_idx
        self.local_training_data = local_training_data
        self.local_test_data = local_test_data
        self.local_sample_number = local_sample_number

        self.args = args
        self.device = device
        self.model_trainer = model_trainer
        self.c_model_local = deepcopy(self.model_trainer.model).cpu()
        for name, params in self.c_model_local.named_parameters():
            params.data = params.data*0


    def update_local_dataset(self, client_idx, local_training_data, local_test_data, local_sample_number):
        self.client_idx = client_idx
        self.local_training_data = local_training_data
        self.local_test_data = local_test_data
        self.local_sample_number = local_sample_number
        self.model_trainer.set_id(client_idx)

    def get_sample_number(self):
        return self.local_sample_number

    ## In client.py (or ScaffoldClient.py)

    def train(self, global_round_idx, group_round_idx, w_global, c_model_global_param):
        """
        Modified to return multiple data points like HFLClient for better wandb logging.
        Returns a list of (global_epoch, weights_delta, c_delta_para) tuples.
        """
        c_model_global_param = deepcopy(c_model_global_param)
        c_model_local_param = self.c_model_local.state_dict()
        self.model_trainer.set_model_params(deepcopy(w_global))

        # Store results for multiple epochs
        w_list = []

        # Check if trainer has the new train_one_epoch_scaffold method
        if hasattr(self.model_trainer, 'train_one_epoch_scaffold'):
            # Train for multiple epochs and capture intermediate states
            total_iteration_cnt = 0
            for epoch in range(self.args.epochs):
                # Train for one epoch
                iteration_cnt = self.model_trainer.train_one_epoch_scaffold(
                    self.local_training_data, self.device, self.args,
                    c_model_global_param, c_model_local_param, epoch
                )
                total_iteration_cnt += iteration_cnt

                # Calculate global epoch index (same logic as HFLClient)
                global_epoch = (
                    global_round_idx * self.args.group_comm_round * self.args.epochs
                    + group_round_idx * self.args.epochs
                    + epoch + 1
                )

                # Capture model state at specified frequency or at the last epoch
                if global_epoch % self.args.frequency_of_the_test == 0 or epoch == self.args.epochs - 1:
                    weights = self.model_trainer.get_model_params()

                    # Calculate deltas for this epoch
                    weights_delta, c_delta_para = self._calculate_deltas(
                        weights, w_global, c_model_global_param, c_model_local_param, total_iteration_cnt
                    )

                    w_list.append((global_epoch, weights_delta, c_delta_para))
        else:
            # Fallback to original behavior if trainer doesn't support epoch-by-epoch training
            iteration_cnt = self.model_trainer.train(self.local_training_data, self.device, self.args, c_model_global_param, c_model_local_param)
            weights = self.model_trainer.get_model_params()

            # --- START OF THE FIX ---
            # Check if training failed completely
            if iteration_cnt == 0:
                logging.warning(f"Client {self.client_idx} had a training failure. Returning zero deltas.")
                # If no iterations were completed, there is no update. Return zero deltas.
                weights_delta = {k: torch.zeros_like(v) for k, v in weights.items()}
                c_delta_para = {k: torch.zeros_like(v) for k, v in c_model_local_param.items()}

                # Create a single entry for the final epoch
                global_epoch = (
                    global_round_idx * self.args.group_comm_round * self.args.epochs
                    + group_round_idx * self.args.epochs
                    + self.args.epochs
                )
                w_list.append((global_epoch, weights_delta, c_delta_para))
                return w_list
            # --- END OF THE FIX ---

            # Calculate deltas for the final result
            weights_delta, c_delta_para = self._calculate_deltas(
                weights, w_global, c_model_global_param, c_model_local_param, iteration_cnt
            )

            # Create a single entry for the final epoch
            global_epoch = (
                global_round_idx * self.args.group_comm_round * self.args.epochs
                + group_round_idx * self.args.epochs
                + self.args.epochs
            )
            w_list.append((global_epoch, weights_delta, c_delta_para))

        return w_list

    def _calculate_deltas(self, weights, w_global, c_model_global_param, c_model_local_param, iteration_cnt):
        """
        Helper method to calculate weight and control variate deltas.
        """
        # This part now only runs if training was successful (iteration_cnt > 0)
        c_new_para = self.c_model_local.cpu().state_dict()
        c_delta_para = {}
        global_model_para = w_global
        net_para = weights
        weights_delta = {}

        for key in net_para:
            # Only calculate deltas for floating-point parameters
            if net_para[key].is_floating_point():
                c_update_val = (global_model_para[key] - net_para[key]) / (iteration_cnt * self.args.learning_rate)
                c_new_para[key] = c_model_local_param[key] - c_model_global_param[key].cpu() + c_update_val
                c_delta_para[key] = c_new_para[key] - c_model_local_param[key]
                weights_delta[key] = net_para[key] - w_global[key].cpu()

        return weights_delta, c_delta_para

    def train_old_interface(self, w_global, c_model_global_param):
        """
        Backward compatibility method for the old interface.
        Returns tuple of (weights_delta, c_delta_para) instead of a list.
        """
        c_model_global_param = deepcopy(c_model_global_param)
        c_model_local_param = self.c_model_local.state_dict()
        self.model_trainer.set_model_params(deepcopy(w_global))

        # This call now correctly uses the robust trainer
        iteration_cnt = self.model_trainer.train(self.local_training_data, self.device, self.args, c_model_global_param, c_model_local_param)
        weights = self.model_trainer.get_model_params()

        # --- START OF THE FIX ---
        # Check if training failed completely
        if iteration_cnt == 0:
            logging.warning(f"Client {self.client_idx} had a training failure. Returning zero deltas.")
            # If no iterations were completed, there is no update. Return zero deltas.
            weights_delta = {k: torch.zeros_like(v) for k, v in weights.items()}
            c_delta_para = {k: torch.zeros_like(v) for k, v in c_model_local_param.items()}
            return weights_delta, c_delta_para
        # --- END OF THE FIX ---

        # Calculate deltas for the final result
        weights_delta, c_delta_para = self._calculate_deltas(
            weights, w_global, c_model_global_param, c_model_local_param, iteration_cnt
        )

        return weights_delta, c_delta_para

    def local_test(self, b_use_test_dataset):
        if b_use_test_dataset:
            test_data = self.local_test_data
        else:
            test_data = self.local_training_data
        metrics = self.model_trainer.test(test_data, self.device, self.args)
        return metrics


