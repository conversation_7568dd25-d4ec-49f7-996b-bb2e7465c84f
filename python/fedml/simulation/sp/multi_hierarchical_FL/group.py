import copy
import logging
import random
import time
import math
import numpy.random
import torch
from .ClientSelection import config
from .ClientSelection.clustered import ClusteredSampling2
from .ClientSelection.divfl import DivFL
from .ClientSelection.grad import GradNorm
from .ClientSelection.loss_based import PowerOfChoice
from .client import HFLClient
from ..fedavg.fedavg_api import FedAvgAPI
from .FedProxClient import Client as FedProxClient
from .ScaffoldClient import Client as ScaffoldClient
from fedml.ml.aggregator.agg_operator import FedMLAggOperator


class Group(FedAvgAPI):
    def __init__(
            self,
            idx,
            total_client_indexes,
            train_data_local_dict,
            test_data_local_dict,
            train_data_local_num_dict,
            args,
            device,
            model,
            model_trainer,
    ):
        self.selection_method = None
        kwargs = {'total': args.client_num_in_total, 'device': device}
        if args.method == 'DivFL':
            assert args.subset_ratio is not None
            self.selection_method = DivFL(**kwargs, subset_ratio=args.subset_ratio)
        elif args.method == 'Cluster2':
            self.selection_method = ClusteredSampling2(**kwargs, dist='L1')
        elif args.method == 'Pow-d':
#            assert args.num_candidates is not None
            self.selection_method = PowerOfChoice(**kwargs, d=30)
        if args.method in config.NEED_SETUP_METHOD:
            self.selection_method.setup(train_data_local_num_dict)

        self.idx = idx
        self.args = args
        self.device = device
        self.client_dict = {}
        self.train_data_local_num_dict = train_data_local_num_dict
        self.diff = 0
        self.w_group = model

        #enconamic cost
        random.seed(self.idx)
        self.run_time = 0
        self.mantaince_cost = 0
        self.computation_cost = 0
        self.communication_cost = 0
        self.computation_unit_cost = random.uniform(0, 0.1)
        self.communication_unit_cost = random.uniform(0.1, 0.3)
        self.mantaince_unit_cost = random.uniform(0, 0.1)
        self.total_cost = 0
        self.model_trainer = model_trainer
        self.c_model_global = None
        if self.args.client_type == "FedProx":
            logging.info("---------------Initialize FedProx Client--------------")
            for client_idx in total_client_indexes:
                self.client_dict[client_idx] = FedProxClient(
                    client_idx,
                    train_data_local_dict[client_idx],
                    test_data_local_dict[client_idx],
                    train_data_local_num_dict[client_idx],
                    args,
                    device,
                    model_trainer,
                )
        elif self.args.client_type == "Scaffold":
            logging.info("---------------Initialize Scaffold Client--------------")
            self.c_model_global = copy.deepcopy(self.model_trainer.model).cpu()
            for name, params in self.c_model_global.named_parameters():
                params.data = params.data*0
            for client_idx in total_client_indexes:
                self.client_dict[client_idx] = ScaffoldClient(
                    client_idx,
                    train_data_local_dict[client_idx],
                    test_data_local_dict[client_idx],
                    train_data_local_num_dict[client_idx],
                    args,
                    device,
                    model_trainer,
                )
        else:
            logging.info("---------------Initialize General Client--------------")
            for client_idx in total_client_indexes:
                self.client_dict[client_idx] = HFLClient(
                    client_idx,
                    train_data_local_dict[client_idx],
                    test_data_local_dict[client_idx],
                    train_data_local_num_dict[client_idx],
                    args,
                    device,
                    model,
                    model_trainer,
                )
        

    def get_sample_number(self, sampled_client_indexes):
        self.group_sample_number = 0
        for client_idx in sampled_client_indexes:
            self.group_sample_number += self.train_data_local_num_dict[client_idx]
        return self.group_sample_number

    def train(self, global_round_idx, w, sampled_client_indexes):
        selected_client_indices = [] #初始化客户端选择的结果的list
        sampled_client_list = []
        if self.args.drop_out_flag:
            drop_out_rate_low = self.args.drop_out_rate
            drop_out_rate = random.uniform(drop_out_rate_low,1)
            sample_client_length = math.ceil(drop_out_rate*len(sampled_client_indexes))
            sampled_client_indexes_temp = random.sample(sampled_client_indexes,sample_client_length)
            sampled_client_list = [self.client_dict[client_idx] for client_idx in sampled_client_indexes_temp]
        else:    
            sampled_client_list = [self.client_dict[client_idx] for client_idx in sampled_client_indexes]#如果不需要客户端选择，就将随机选择的结果中属于该group的客户端加入到sampled_client_list中
        selected_client_num = len(sampled_client_list)#存储该group的被选择客户端数量
        #如果设置了客户端选择，就取消之前的随机选择，将客户端list初始化为所有客户端
        if self.selection_method is not None:
            #let self.client_dict to a list 这个不能变
            sampled_client_list = list(self.client_dict.values())

        w_group = w
        self.model_trainer.set_model_params(w_group)
        w_group_list = []
        start_timestamp = time.time()
        for group_round_idx in range(self.args.group_comm_round):
            client_selected_results = sampled_client_list
            #logging.info("Group ID : {} / Group Communication Round : {}".format(self.idx, group_round_idx))
            w_locals_dict = {}
            #client selecttion method
            client_indices = [client.client_idx for client in sampled_client_list] #把该组所有的client的id放入client_indices中
            # sampled_client_indexes is the client ids of this group
            if self.args.method in config.NEED_INIT_METHOD:
                local_models = [client.get_model() for client in sampled_client_list]
                self.selection_method.init(self.w_group, local_models)
                del local_models
            # Prepare for PoW-d client selection
            if self.args.method in config.CANDIDATE_SELECTION_METHOD:
                # np.random.seed((self.args.seed+1)*10000000 + round_idx)
                #print(f'> candidate client selection {self.args.num_candidates}/{len(client_indices)}')
                client_indices = self.selection_method.select_candidates(client_indices, selected_client_num)
                client_selected_results = [self.client_dict[client_idx] for client_idx in client_indices]
            # Prepare for Cluster2 client selection
            if self.args.method in config.PRE_SELECTION_METHOD:
                pre_selected_client_indices = self.selection_method.select(20, client_indices, None)
                temp_selected_client = []
                for client in sampled_client_list:
                    if client.client_idx in pre_selected_client_indices:
                        temp_selected_client.append(client)
                #从temp_selected_client中随机选择selected_client_num个client
                random.seed(self.idx)
                random.shuffle(temp_selected_client)
                temp_selected_client = temp_selected_client[:selected_client_num]
                client_selected_results = temp_selected_client

            w_locals = [] #We use this list to store model updates from clients
            # train each client with FedProx
            if self.args.client_type == "FedProx":
                w_temp = self.model_trainer.get_model_params()
                for client in client_selected_results:
                    w = client.train(copy.deepcopy(w_temp))
                    w_locals.append((client.get_sample_number(), copy.deepcopy(w)))
            # train each client with Scaffold
            elif self.args.client_type == "Scaffold":   
                w_temp = self.model_trainer.get_model_params()
                for client in client_selected_results:
                    weights_delta, c_delta_para = client.train(copy.deepcopy(w_temp), self.c_model_global.state_dict())
                    w_locals.append((client.get_sample_number(), copy.deepcopy(weights_delta), copy.deepcopy(c_delta_para)))   
            # train each client with fedavg    
            else:
                for client in client_selected_results:
                    w_local_list = client.train(global_round_idx, group_round_idx, w_group)
                    for global_epoch, w_temp in w_local_list:
                        if not global_epoch in w_locals_dict:
                            w_locals_dict[global_epoch] = []
                        w_locals_dict[global_epoch].append((client.get_sample_number(), w_temp))


            #训练后利用model选择
            if self.args.method not in config.PRE_SELECTION_METHOD:
                #print(f'> post-client selection {self.num_clients_per_round}/{len(client_indices)}')
                kwargs = {'n': selected_client_num, 'client_idxs': client_indices, 'round': group_round_idx}
                # select by local models(gradients)
                # Prepare for DivFL client selection
                if self.args.method in config.NEED_LOCAL_MODELS_METHOD:
                    local_models = [self.client_dict[idx].get_model() for idx in client_indices]
                    selected_client_indices = self.selection_method.select(**kwargs, metric=local_models)
                    del local_models
                #print(selected_client_indices)
            # aggregate local weights in original FedProx
            if self.args.client_type == "FedProx":
                global_epoch = (
                        global_round_idx * self.args.group_comm_round * self.args.epochs
                        + (group_round_idx) * self.args.epochs + 1
                    )
                w_group_agg = self.FedProx_Agg(w_locals)
                w_group_list.append((global_epoch, w_group_agg))
            # aggregate local weights in original Scaffold
            elif self.args.client_type == "Scaffold": 
                global_epoch = (
                        global_round_idx * self.args.group_comm_round * self.args.epochs
                        + (group_round_idx) * self.args.epochs + 1
                    )
                total_weights_delta, total_c_delta_para = self.Scaffold_Agg(w_locals)
                c_global_para = self.c_model_global.state_dict()
                for key in c_global_para:
                    if c_global_para[key].type() == 'torch.LongTensor':
                        c_global_para[key] += total_c_delta_para[key].type(torch.LongTensor)
                    elif c_global_para[key].type() == 'torch.cuda.LongTensor':
                        c_global_para[key] += total_c_delta_para[key].type(torch.cuda.LongTensor)
                    else:
                        c_global_para[key] += total_c_delta_para[key]
                self.c_model_global.load_state_dict(c_global_para)
                w_temp = self.model_trainer.get_model_params()
                for key in w_temp.keys():
                    w_temp[key] += total_weights_delta[key]

                self.model_trainer.set_model_params(w_temp)
                w_group_list.append((global_epoch, copy.deepcopy(self.model_trainer.get_model_params())))
            # aggregate local weights in original FedAvg
            else:
                for global_epoch in sorted(w_locals_dict.keys()):
                    w_locals = w_locals_dict[global_epoch]
                    w_group_list.append((global_epoch, self._aggregate(w_locals)))
            if w_group_list == []:
                pass
            # update the group weight
            # 由于DivFL需要所有客户端先训练然后再选择，所以在这里需要把对应的客户端模型选出来进行聚合并产生下一轮的w_group
            if self.args.method == "DivFL":
                # 便利所有的client，如果client的id在selected_client_indices中，就将其加入到w_locals中
                w_locals = []
                for id in selected_client_indices:
                    client = sampled_client_list[id]
                    w_locals.append((client.get_sample_number(),
                                        client.model.state_dict()))
                    print(len(w_locals))
                    w_group = self._aggregate(w_locals)
            else:#其他方法只训练的选择的客户端，可以直接从w_group_list中取出最后一个w_group
                w_group = w_group_list[-1][1]
        
        self.w_group.load_state_dict(w_group)
        end_timestamp = time.time()
        random.seed(self.idx)
        self.set_run_time(self.args.group_comm_round * random.uniform(0.1, 0.3))
        self.set_computation_cost(self.computation_unit_cost * self.args.group_comm_round)
        self.set_communication_cost(self.communication_unit_cost * self.args.group_comm_round)
        return w_group_list

    def calculate_cost(self):
        self.total_cost = self.mantaince_cost + self.computation_cost + self.communication_cost
        return self.total_cost
    def set_run_time(self, run_time):
        mantaince_cost = random.randint(1, 10)
        self.run_time = run_time
        self.mantaince_cost = mantaince_cost * run_time

    def set_computation_cost(self, param):
        self.computation_cost = param

    def set_communication_cost(self, param):
        self.communication_cost = param

    def FedProx_Agg(self, w_locals):
        training_num = 0
        for i in range(len(w_locals)):
            local_sample_num, local_model_params = w_locals[i]
            training_num += local_sample_num
            
        def fff(args, raw_grad_list, training_num):
            (num0, avg_params) = raw_grad_list[0]
            for k in avg_params.keys():
                for i in range(0, len(raw_grad_list)):
                    local_sample_number, local_model_params = raw_grad_list[i]
                    w = local_sample_number / training_num
                    if i == 0:
                        avg_params[k] = local_model_params[k] * w
                    else:
                        avg_params[k] += local_model_params[k] * w
            return avg_params
        
        avg_params = fff(self.args, w_locals, training_num)
        return avg_params

    def Scaffold_Agg(self, w_locals):
        # training_num = 0
        # for idx in range(len(w_locals)):
        #     (sample_num, averaged_params) = w_locals[idx]
        #     training_num += sample_num
        training_num = 0
        for i in range(len(w_locals)):
                local_sample_num, _, _ = w_locals[i]
                training_num += local_sample_num
        def fff(args, raw_grad_list, training_num):
            (num0, total_weights_delta, total_c_delta_para) = raw_grad_list[0]
            # avg_params = total_weights_delta
            for k in total_weights_delta.keys():
                for i in range(0, len(raw_grad_list)):
                    local_sample_number, weights_delta, c_delta_para = raw_grad_list[i]
                    w = local_sample_number / training_num
                    # w = local_sample_number / len(raw_grad_list)
                    if i == 0:
                        total_weights_delta[k] = weights_delta[k] * w
                        total_c_delta_para[k] = c_delta_para[k]
                    else:
                        total_weights_delta[k] += weights_delta[k] * w
                        total_c_delta_para[k] += c_delta_para[k]
                # w_c = 1 / args.client_num_in_total
                w_c = 1 / args.client_num_in_total
                total_weights_delta[k] = weights_delta[k]
                total_c_delta_para[k] = c_delta_para[k] * w_c
            avg_params = (total_weights_delta, total_c_delta_para)
            return avg_params
        avg_params = fff(self.args, w_locals,training_num)
        # logging.info(f"avg_params:{avg_params}. len(avg_params): {len(avg_params)}")
        (total_weights_delta, total_c_delta_para) = avg_params
        return total_weights_delta, total_c_delta_para