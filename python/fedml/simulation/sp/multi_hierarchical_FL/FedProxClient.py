import copy
import logging

class Client:
    def __init__(
        self, client_idx, local_training_data, local_test_data, local_sample_number, args, device, model_trainer,
    ):
        self.client_idx = client_idx
        self.local_training_data = local_training_data
        self.local_test_data = local_test_data
        self.local_sample_number = local_sample_number

        self.args = args
        self.device = device
        self.model_trainer = model_trainer

    def update_local_dataset(self, client_idx, local_training_data, local_test_data, local_sample_number):
        self.client_idx = client_idx
        self.local_training_data = local_training_data
        self.local_test_data = local_test_data
        self.local_sample_number = local_sample_number
        self.model_trainer.set_id(client_idx)

    def get_sample_number(self):
        return self.local_sample_number

    def train(self, global_round_idx, group_round_idx, w_global):
        """
        Modified to return multiple data points like HFLClient for better wandb logging.
        Returns a list of (global_epoch, weights) tuples.
        """
        self.model_trainer.set_model_params(w_global)

        # Store initial weights to track progress
        w_list = []

        # Check if trainer has the new train_one_epoch method
        if hasattr(self.model_trainer, 'train_one_epoch'):
            # Train for multiple epochs and capture intermediate states
            for epoch in range(self.args.epochs):
                # Train for one epoch
                self.model_trainer.train_one_epoch(self.local_training_data, self.device, self.args, epoch)

                # Calculate global epoch index (same logic as HFLClient)
                global_epoch = (
                    global_round_idx * self.args.group_comm_round * self.args.epochs
                    + group_round_idx * self.args.epochs
                    + epoch + 1
                )

                # Capture model state at specified frequency or at the last epoch
                if global_epoch % self.args.frequency_of_the_test == 0 or epoch == self.args.epochs - 1:
                    weights = copy.deepcopy(self.model_trainer.get_model_params())
                    w_list.append((global_epoch, weights))
        else:
            # Fallback to original behavior if trainer doesn't support epoch-by-epoch training
            self.model_trainer.train(self.local_training_data, self.device, self.args)
            weights = self.model_trainer.get_model_params()

            # Create a single entry for the final epoch
            global_epoch = (
                global_round_idx * self.args.group_comm_round * self.args.epochs
                + group_round_idx * self.args.epochs
                + self.args.epochs
            )
            w_list.append((global_epoch, weights))

        return w_list

    def train_old_interface(self, w_global):
        """
        Backward compatibility method for the old interface.
        Returns single weights instead of a list.
        """
        self.model_trainer.set_model_params(w_global)
        self.model_trainer.train(self.local_training_data, self.device, self.args)
        weights = self.model_trainer.get_model_params()
        return weights

    def local_test(self, b_use_test_dataset):
        if b_use_test_dataset:
            test_data = self.local_test_data
        else:
            test_data = self.local_training_data
        metrics = self.model_trainer.test(test_data, self.device, self.args)
        return metrics
