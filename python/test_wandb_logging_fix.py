#!/usr/bin/env python3
"""
Test script to verify that FedProx and Scaffold clients now return multiple data points
like HFLClient for better wandb logging.
"""

import sys
import os
import torch
import torch.nn as nn
import copy
from unittest.mock import MagicMock

# Add the fedml path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import the clients
from fedml.simulation.sp.multi_hierarchical_FL.client import HFLClient
from fedml.simulation.sp.multi_hierarchical_FL.FedProxClient import Client as FedProxClient
from fedml.simulation.sp.multi_hierarchical_FL.ScaffoldClient import Client as ScaffoldClient
from fedml.ml.trainer.fedprox_trainer import FedProxModelTrainer
from fedml.ml.trainer.scaffold_trainer import ScaffoldModelTrainer

class SimpleModel(nn.Module):
    def __init__(self, input_size=10, hidden_size=5, output_size=2):
        super(SimpleModel, self).__init__()
        self.linear = nn.Linear(input_size, hidden_size)
        self.output = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        x = torch.relu(self.linear(x))
        return self.output(x)

class MockArgs:
    def __init__(self):
        self.epochs = 3
        self.frequency_of_the_test = 1
        self.group_comm_round = 2
        self.client_optimizer = "sgd"
        self.lr = 0.01
        self.learning_rate = 0.01
        self.weight_decay = 0.0001
        self.wd = 0.0001
        self.fedprox_mu = 0.01

def create_mock_data():
    """Create mock training data"""
    data = []
    for _ in range(5):  # 5 batches
        x = torch.randn(4, 10)  # batch_size=4, input_size=10
        y = torch.randint(0, 2, (4,))  # binary classification
        data.append((x, y))
    return data

def test_hfl_client():
    """Test HFLClient returns multiple data points"""
    print("Testing HFLClient...")
    
    model = SimpleModel()
    trainer = MagicMock()
    args = MockArgs()
    device = torch.device("cpu")
    
    train_data = create_mock_data()
    test_data = create_mock_data()
    
    client = HFLClient(
        client_idx=0,
        local_training_data=train_data,
        local_test_data=test_data,
        local_sample_number=20,
        args=args,
        device=device,
        model=model,
        model_trainer=trainer
    )
    
    w_global = model.state_dict()
    result = client.train(global_round_idx=0, group_round_idx=0, w=w_global)
    
    print(f"HFLClient returned {len(result)} data points")
    for i, (epoch, weights) in enumerate(result):
        print(f"  Data point {i+1}: epoch={epoch}, weights_keys={list(weights.keys())}")
    
    return len(result) > 1

def test_fedprox_client():
    """Test FedProxClient returns multiple data points"""
    print("\nTesting FedProxClient...")
    
    model = SimpleModel()
    trainer = FedProxModelTrainer(model)
    trainer.set_id(0)
    args = MockArgs()
    device = torch.device("cpu")
    
    train_data = create_mock_data()
    test_data = create_mock_data()
    
    client = FedProxClient(
        client_idx=0,
        local_training_data=train_data,
        local_test_data=test_data,
        local_sample_number=20,
        args=args,
        device=device,
        model_trainer=trainer
    )
    
    w_global = model.state_dict()
    result = client.train(global_round_idx=0, group_round_idx=0, w_global=w_global)
    
    print(f"FedProxClient returned {len(result)} data points")
    for i, (epoch, weights) in enumerate(result):
        print(f"  Data point {i+1}: epoch={epoch}, weights_keys={list(weights.keys())}")
    
    return len(result) > 1

def test_scaffold_client():
    """Test ScaffoldClient returns multiple data points"""
    print("\nTesting ScaffoldClient...")
    
    model = SimpleModel()
    trainer = ScaffoldModelTrainer(model)
    trainer.set_id(0)
    args = MockArgs()
    device = torch.device("cpu")
    
    train_data = create_mock_data()
    test_data = create_mock_data()
    
    client = ScaffoldClient(
        client_idx=0,
        local_training_data=train_data,
        local_test_data=test_data,
        local_sample_number=20,
        args=args,
        device=device,
        model_trainer=trainer
    )
    
    w_global = model.state_dict()
    c_global = copy.deepcopy(model.state_dict())
    # Zero out control variates
    for key in c_global:
        c_global[key] = torch.zeros_like(c_global[key])
    
    result = client.train(
        global_round_idx=0, 
        group_round_idx=0, 
        w_global=w_global, 
        c_model_global_param=c_global
    )
    
    print(f"ScaffoldClient returned {len(result)} data points")
    for i, (epoch, weights_delta, c_delta) in enumerate(result):
        print(f"  Data point {i+1}: epoch={epoch}, weights_delta_keys={list(weights_delta.keys())}, c_delta_keys={list(c_delta.keys())}")
    
    return len(result) > 1

def main():
    """Run all tests"""
    print("Testing wandb logging improvements for FedProx and Scaffold clients")
    print("=" * 70)
    
    try:
        hfl_success = test_hfl_client()
        fedprox_success = test_fedprox_client()
        scaffold_success = test_scaffold_client()
        
        print("\n" + "=" * 70)
        print("RESULTS:")
        print(f"HFLClient multiple data points: {'✓' if hfl_success else '✗'}")
        print(f"FedProxClient multiple data points: {'✓' if fedprox_success else '✗'}")
        print(f"ScaffoldClient multiple data points: {'✓' if scaffold_success else '✗'}")
        
        if fedprox_success and scaffold_success:
            print("\n🎉 SUCCESS: Both FedProx and Scaffold clients now return multiple data points!")
            print("This should enable better wandb logging with curves instead of single points.")
        else:
            print("\n❌ FAILURE: Some clients are not returning multiple data points.")
            
    except Exception as e:
        print(f"\n❌ ERROR during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
